<script>
	import { <PERSON><PERSON>, Position } from '@xyflow/svelte';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import { Button } from '$lib/components/ui/button';
	import { Badge } from '$lib/components/ui/badge';
	import { getNodeDisplayValue } from './nodeDefinitions.js';
	import FilterNodeDialog from './FilterNodeDialog.svelte';

	let { data } = $props();

	// Local state
	let currentValue = $state(data.currentValue || data.defaultValue);
	let dialogOpen = $state(false);

	// Sync with prop changes
	$effect(() => {
		if (data.currentValue !== undefined) {
			currentValue = data.currentValue;
		}
	});

	// Handle value changes
	function handleValueChange(newValue) {
		currentValue = newValue;
		
		// Notify parent of changes
		if (data.onValueChange) {
			data.onValueChange({
				nodeId: data.instanceId,
				newValue: currentValue
			});
		}
	}

	// Handle node click to open configuration dialog
	function handleNodeClick(event) {
		event.stopPropagation();
		dialogOpen = true;
	}

	// Handle delete
	function handleDelete(event) {
		event.stopPropagation();
		if (data.onDelete) {
			data.onDelete(data.instanceId);
		}
	}

	// Get display value for the node
	const displayValue = $derived(getNodeDisplayValue({ data: { ...data, currentValue } }));

	// Get background color with opacity
	const backgroundColor = $derived(`rgba(${hexToRgb(data.color)}, 0.1)`);
	const borderColor = $derived(data.color);

	function hexToRgb(hex) {
		const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
		if (result) {
			const r = parseInt(result[1], 16);
			const g = parseInt(result[2], 16);
			const b = parseInt(result[3], 16);
			return `${r}, ${g}, ${b}`;
		}
		return '0, 0, 0';
	}
</script>

<div 
	class="filter-node relative cursor-pointer transition-all duration-200 hover:shadow-lg"
	style="background: {backgroundColor}; border: 2px solid {borderColor}; border-radius: 8px; width: 240px;"
	onclick={handleNodeClick}
>
	<!-- Input handle -->
	<Handle 
		type="target" 
		position={Position.Left} 
		style="width: 10px; height: 10px; background: {data.color}; border: 2px solid white;" 
	/>
	
	<!-- Output handle -->
	<Handle 
		type="source" 
		position={Position.Right} 
		style="width: 10px; height: 10px; background: {data.color}; border: 2px solid white;" 
	/>

	<!-- Delete button (if deletable) -->
	{#if data.deletable}
		<Button
			variant="ghost"
			size="sm"
			class="absolute -top-2 -right-2 h-6 w-6 p-0 bg-red-500 text-white hover:bg-red-600 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
			onclick={handleDelete}
		>
			×
		</Button>
	{/if}

	<div class="p-3">
		<!-- Header with icon and title -->
		<div class="flex items-center gap-2 mb-2">
			<span class="text-lg">{data.icon}</span>
			<div class="flex-1 min-w-0">
				<div class="text-sm font-semibold text-gray-800 truncate">{data.title}</div>
			</div>
			<span class="text-xs text-gray-500">⚙️</span>
		</div>

		<!-- Value display -->
		<div class="bg-white/50 rounded p-2 border border-gray-200">
			<div class="text-xs font-medium text-gray-800">
				{displayValue}
			</div>
		</div>

		<!-- Quick status indicators based on node type -->
		<div class="mt-2 flex flex-wrap gap-1">
			{#if data.id === 'songs-and-types'}
				{#if currentValue.openings}<Badge variant="secondary" class="text-xs">OP</Badge>{/if}
				{#if currentValue.endings}<Badge variant="secondary" class="text-xs">ED</Badge>{/if}
				{#if currentValue.inserts}<Badge variant="secondary" class="text-xs">IN</Badge>{/if}
			{:else if data.id === 'anime-type'}
				{#each Object.keys(currentValue).filter(key => currentValue[key]) as type}
					<Badge variant="secondary" class="text-xs uppercase">{type}</Badge>
				{/each}
			{:else if data.id === 'vintage'}
				{#if currentValue.ranges && currentValue.ranges.length > 0}
					<Badge variant="secondary" class="text-xs">
						{currentValue.ranges.length} range{currentValue.ranges.length > 1 ? 's' : ''}
					</Badge>
				{/if}
			{:else if ['song-difficulty', 'player-score', 'anime-score'].includes(data.id)}
				<Badge variant="secondary" class="text-xs">
					{currentValue.mode || 'range'}
				</Badge>
			{:else if data.id === 'song-categories'}
				{#if currentValue.included && currentValue.included.length > 0}
					<Badge variant="secondary" class="text-xs">+{currentValue.included.length}</Badge>
				{/if}
				{#if currentValue.excluded && currentValue.excluded.length > 0}
					<Badge variant="destructive" class="text-xs">-{currentValue.excluded.length}</Badge>
				{/if}
			{/if}
		</div>
	</div>
</div>

<!-- Configuration Dialog -->
<FilterNodeDialog
	bind:open={dialogOpen}
	nodeData={{...data, formType: data.formType || 'simple'}}
	bind:value={currentValue}
	onSave={handleValueChange}
/>

<style>
	.filter-node {
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	}
	
	.filter-node:hover {
		transform: translateY(-1px);
	}
	
	.filter-node:hover .absolute {
		opacity: 1;
	}
</style>
