<script>
	import { <PERSON><PERSON>, <PERSON>sition } from '@xyflow/svelte';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '$lib/components/ui/select';
	import { getNodeDisplayValue } from './nodeDefinitions.js';

	let { data } = $props();

	// Local state for settings
	let settings = $state(data.currentValue || {});
	let isExpanded = $state(false);

	// Sync with prop changes
	$effect(() => {
		if (data.currentValue) {
			settings = { ...data.currentValue };
		}
	});

	// Handle setting changes
	function updateSetting(key, newValue) {
		settings[key] = { ...settings[key], value: newValue };
		
		// Notify parent of changes
		if (data.onValueChange) {
			data.onValueChange({
				nodeId: data.instanceId,
				newValue: settings
			});
		}
	}

	// Toggle expanded view
	function toggleExpanded() {
		isExpanded = !isExpanded;
	}

	// Get display value for the node
	$: displayValue = getNodeDisplayValue({ data: { ...data, currentValue: settings } });
</script>

<div 
	class="basic-settings-node relative transition-all duration-200"
	class:expanded={isExpanded}
	style="background: rgba(99, 102, 241, 0.1); border: 2px solid {data.color}; border-radius: 12px; min-width: {isExpanded ? '400px' : '280px'}; max-width: {isExpanded ? '500px' : '280px'};"
>
	<!-- Output handle -->
	<Handle 
		type="source" 
		position={Position.Right} 
		style="width: 12px; height: 12px; background: {data.color}; border: 2px solid white;" 
	/>

	<Card class="border-0 shadow-none bg-transparent">
		<CardHeader class="pb-3">
			<div class="flex items-center justify-between">
				<CardTitle class="flex items-center gap-2 text-lg font-semibold text-gray-800">
					<span class="text-xl">{data.icon}</span>
					{isExpanded ? data.title : displayValue}
				</CardTitle>
				<Button 
					variant="ghost" 
					size="sm"
					onclick={toggleExpanded}
					class="h-6 w-6 p-0"
				>
					{isExpanded ? '−' : '+'}
				</Button>
			</div>
			{#if isExpanded}
				<p class="text-sm text-gray-600">{data.description}</p>
			{/if}
		</CardHeader>

		{#if isExpanded}
			<CardContent class="pt-0 space-y-4">
				<!-- Mode Settings Section -->
				<div class="space-y-3">
					<h4 class="text-sm font-medium text-gray-700 border-b pb-1">Mode Settings</h4>
					
					<!-- Scoring -->
					<div class="space-y-1">
						<Label for="scoring" class="text-xs">Scoring Method</Label>
						<Select 
							value={settings.scoring?.value || 'count'}
							onValueChange={(value) => updateSetting('scoring', value)}
						>
							<SelectTrigger class="h-8">
								<SelectValue />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="count">Count</SelectItem>
								<SelectItem value="hint">Hint</SelectItem>
								<SelectItem value="speed">Speed</SelectItem>
							</SelectContent>
						</Select>
					</div>

					<!-- Answering -->
					<div class="space-y-1">
						<Label for="answering" class="text-xs">Answering Method</Label>
						<Select 
							value={settings.answering?.value || 'typing'}
							onValueChange={(value) => updateSetting('answering', value)}
						>
							<SelectTrigger class="h-8">
								<SelectValue />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="typing">Typing</SelectItem>
								<SelectItem value="mix">Mix</SelectItem>
								<SelectItem value="multiple-choice">Multiple Choice</SelectItem>
							</SelectContent>
						</Select>
					</div>
				</div>

				<!-- General Settings Section -->
				<div class="space-y-3">
					<h4 class="text-sm font-medium text-gray-700 border-b pb-1">General Settings</h4>
					
					<!-- Players -->
					<div class="space-y-1">
						<Label for="players" class="text-xs">Number of Players</Label>
						<Input 
							type="number" 
							min="1" 
							max="100"
							value={settings.players?.value || 8}
							onchange={(e) => updateSetting('players', parseInt(e.target.value))}
							class="h-8"
						/>
					</div>

					<!-- Team Size -->
					<div class="space-y-1">
						<Label for="teamSize" class="text-xs">Team Size</Label>
						<Input 
							type="number" 
							min="1" 
							max="8"
							value={settings.teamSize?.value || 1}
							onchange={(e) => updateSetting('teamSize', parseInt(e.target.value))}
							class="h-8"
						/>
					</div>

					<!-- Guess Time -->
					<div class="space-y-1">
						<Label for="guessTime" class="text-xs">Guess Time (seconds)</Label>
						<Input 
							type="number" 
							min="5" 
							max="99"
							value={settings.guessTime?.value || 20}
							onchange={(e) => updateSetting('guessTime', parseInt(e.target.value))}
							class="h-8"
						/>
					</div>
				</div>

				<!-- Audio Settings Section -->
				<div class="space-y-3">
					<h4 class="text-sm font-medium text-gray-700 border-b pb-1">Audio Settings</h4>
					
					<!-- Sample Point -->
					<div class="space-y-1">
						<Label class="text-xs">Sample Point</Label>
						<div class="text-xs text-gray-600">
							{#if settings.samplePoint?.value?.useRange}
								Range: {settings.samplePoint.value.start || 1}% - {settings.samplePoint.value.end || 100}%
							{:else}
								Static: {settings.samplePoint?.value?.staticValue || 20}%
							{/if}
						</div>
						<Button variant="outline" size="sm" class="h-6 text-xs">
							Configure
						</Button>
					</div>

					<!-- Playback Speed -->
					<div class="space-y-1">
						<Label for="playbackSpeed" class="text-xs">Playback Speed</Label>
						<Select 
							value={settings.playbackSpeed?.value?.toString() || '1.0'}
							onValueChange={(value) => updateSetting('playbackSpeed', parseFloat(value))}
						>
							<SelectTrigger class="h-8">
								<SelectValue />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="0.25">0.25x</SelectItem>
								<SelectItem value="0.5">0.5x</SelectItem>
								<SelectItem value="0.75">0.75x</SelectItem>
								<SelectItem value="1.0">1.0x</SelectItem>
								<SelectItem value="1.25">1.25x</SelectItem>
								<SelectItem value="1.5">1.5x</SelectItem>
								<SelectItem value="2.0">2.0x</SelectItem>
							</SelectContent>
						</Select>
					</div>

					<!-- Modifiers -->
					<div class="space-y-1">
						<Label class="text-xs">Modifiers</Label>
						<div class="text-xs text-gray-600">
							{settings.modifiers?.value?.length || 0} active
						</div>
						<Button variant="outline" size="sm" class="h-6 text-xs">
							Configure
						</Button>
					</div>
				</div>
			</CardContent>
		{/if}
	</Card>
</div>

<style>
	.basic-settings-node {
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
	}
	
	.basic-settings-node.expanded {
		box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
	}
</style>
