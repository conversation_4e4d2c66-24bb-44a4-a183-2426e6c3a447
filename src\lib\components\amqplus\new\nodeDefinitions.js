// Core node system architecture for the new editor

// Node categories and their properties
export const NODE_CATEGORIES = {
	BASIC_SETTINGS: 'basicSettings',
	FILTER: 'filter',
	NUMBER_OF_SONGS: 'numberOfSongs'
};

// Basic Settings node configuration (non-deletable, starting point)
export const BASIC_SETTINGS_CONFIG = {
	id: 'basic-settings',
	type: NODE_CATEGORIES.BASIC_SETTINGS,
	title: 'Basic Settings',
	description: 'Core lobby configuration settings',
	icon: '⚙️',
	color: '#6366f1',
	deletable: false,
	unique: true,
	position: { x: 100, y: 100 },
	settings: {
		// Mode Settings
		scoring: { value: 'count', label: 'Scoring', type: 'select', options: ['count', 'hint', 'speed'] },
		answering: { value: 'typing', label: 'Answering', type: 'select', options: ['typing', 'mix', 'multiple-choice'] },
		
		// General Settings  
		players: { value: 8, label: 'Players', type: 'number', min: 1, max: 100 },
		teamSize: { value: 1, label: 'Team Size', type: 'number', min: 1, max: 8 },
		guessTime: { value: 20, label: 'Guess Time', type: 'number', min: 5, max: 99 },
		
		// Audio Settings
		samplePoint: { value: { useRange: false, staticValue: 20 }, label: 'Sample Point', type: 'complex' },
		playbackSpeed: { value: 1.0, label: 'Playback Speed', type: 'number', min: 0.25, max: 2.0, step: 0.25 },
		
		// Modifiers
		modifiers: { value: [], label: 'Modifiers', type: 'complex' }
	}
};

// Filter node definitions (can be added multiple times)
export const FILTER_NODE_DEFINITIONS = {
	'songs-and-types': {
		id: 'songs-and-types',
		type: NODE_CATEGORIES.FILTER,
		title: 'Songs & Types',
		description: 'Filter songs by opening/ending types and selection criteria',
		icon: '🎵',
		color: '#10b981',
		deletable: true,
		unique: false,
		formType: 'complex-songs-and-types',
		defaultValue: {
			mode: 'count',
			songTypes: {
				openings: { enabled: true, count: 10, percentage: 50, random: false },
				endings: { enabled: true, count: 10, percentage: 50, random: false },
				inserts: { enabled: false, count: 0, percentage: 0, random: false }
			},
			songSelection: {
				random: { value: 50, randomRange: false, min: 40, max: 60 },
				watched: { value: 50, randomRange: false, min: 40, max: 60 }
			}
		}
	},
	
	'anime-type': {
		id: 'anime-type',
		type: NODE_CATEGORIES.FILTER,
		title: 'Anime Type',
		description: 'Filter by anime format (TV, Movie, OVA, etc.)',
		icon: '📺',
		color: '#f59e0b',
		deletable: true,
		unique: false,
		formType: 'complex-checkbox',
		defaultValue: {
			tv: true,
			movie: true,
			ova: true,
			ona: true,
			special: true
		}
	},
	
	'vintage': {
		id: 'vintage',
		type: NODE_CATEGORIES.FILTER,
		title: 'Vintage',
		description: 'Filter by anime release year and season',
		icon: '📅',
		color: '#8b5cf6',
		deletable: true,
		unique: false,
		formType: 'complex-vintage',
		defaultValue: {
			ranges: [{
				from: { season: 'Winter', year: 1944 },
				to: { season: 'Fall', year: 2025 }
			}]
		}
	},
	
	'song-difficulty': {
		id: 'song-difficulty',
		type: NODE_CATEGORIES.FILTER,
		title: 'Song Difficulty',
		description: 'Filter songs by difficulty rating',
		icon: '⭐',
		color: '#ef4444',
		deletable: true,
		unique: false,
		formType: 'complex-song-difficulty',
		defaultValue: {
			viewMode: 'basic',
			easy: { enabled: true, count: 5, percentage: 25, randomRange: false, min: 20, max: 30 },
			medium: { enabled: true, count: 10, percentage: 50, randomRange: false, min: 40, max: 60 },
			hard: { enabled: true, count: 5, percentage: 25, randomRange: false, min: 70, max: 80 },
			ranges: []
		}
	},

	'player-score': {
		id: 'player-score',
		type: NODE_CATEGORIES.FILTER,
		title: 'Player Score',
		description: 'Filter by player score requirements',
		icon: '🏆',
		color: '#f97316',
		deletable: true,
		unique: false,
		formType: 'complex-score-range',
		defaultValue: {
			min: 0,
			max: 100,
			mode: 'range',
			percentages: {}
		}
	},

	'anime-score': {
		id: 'anime-score',
		type: NODE_CATEGORIES.FILTER,
		title: 'Anime Score',
		description: 'Filter by anime rating/score',
		icon: '⭐',
		color: '#06b6d4',
		deletable: true,
		unique: false,
		formType: 'complex-score-range',
		defaultValue: {
			min: 2,
			max: 10,
			mode: 'range',
			percentages: {}
		}
	},
	
	'song-categories': {
		id: 'song-categories',
		type: NODE_CATEGORIES.FILTER,
		title: 'Song Categories',
		description: 'Filter by song categories and tags',
		icon: '🏷️',
		color: '#ec4899',
		deletable: true,
		unique: false,
		formType: 'complex-genres-tags',
		defaultValue: {
			mode: 'basic',
			included: [],
			excluded: [],
			optional: [],
			percentages: {},
			counts: {}
		}
	}
};

// Number of Songs node (unique, must be last)
export const NUMBER_OF_SONGS_CONFIG = {
	id: 'number-of-songs',
	type: NODE_CATEGORIES.NUMBER_OF_SONGS,
	title: 'Number of Songs',
	description: 'Determines final song count for the lobby',
	icon: '🔢',
	color: '#dc2626',
	deletable: false,
	unique: true,
	mustBeLast: true,
	defaultValue: 20
};

// Helper functions for node management
export function createNodeInstance(definition, position = { x: 0, y: 0 }, instanceId = null) {
	const id = instanceId || `${definition.id}-${Date.now()}`;
	
	return {
		id,
		type: definition.type,
		position,
		data: {
			...definition,
			instanceId: id,
			currentValue: definition.defaultValue || definition.settings || definition.defaultValue
		},
		deletable: definition.deletable
	};
}

export function getNodeDisplayValue(node) {
	const { data } = node;
	
	// For basic settings, show a summary
	if (data.type === NODE_CATEGORIES.BASIC_SETTINGS) {
		const settings = data.currentValue;
		return `${settings.scoring?.value || 'count'} | ${settings.players?.value || 8} players`;
	}
	
	// For number of songs, show the count
	if (data.type === NODE_CATEGORIES.NUMBER_OF_SONGS) {
		return `${data.currentValue} songs`;
	}
	
	// For filter nodes, show relevant info based on type
	const value = data.currentValue;
	
	switch (data.id) {
		case 'songs-and-types':
			const enabledTypes = Object.keys(value).filter(key => value[key] === true);
			return enabledTypes.length > 0 ? enabledTypes.join(', ') : 'None selected';
			
		case 'anime-type':
			const enabledAnimeTypes = Object.keys(value).filter(key => value[key] === true);
			return enabledAnimeTypes.length > 0 ? enabledAnimeTypes.join(', ') : 'None selected';
			
		case 'vintage':
			if (value.ranges && value.ranges.length > 0) {
				const range = value.ranges[0];
				return `${range.from.year} - ${range.to.year}`;
			}
			return 'All years';
			
		case 'song-difficulty':
		case 'player-score':
		case 'anime-score':
			return `${value.min} - ${value.max}`;
			
		case 'song-categories':
			const total = (value.included?.length || 0) + (value.excluded?.length || 0);
			return total > 0 ? `${total} categories` : 'All categories';
			
		default:
			return 'Configured';
	}
}

// Default template configuration
export const DEFAULT_TEMPLATE = {
	nodes: [
		{
			...BASIC_SETTINGS_CONFIG,
			position: { x: 100, y: 100 }
		},
		{
			...createNodeInstance(FILTER_NODE_DEFINITIONS['songs-and-types'], { x: 450, y: 100 }),
		},
		{
			...createNodeInstance(FILTER_NODE_DEFINITIONS['anime-type'], { x: 800, y: 100 }),
		},
		{
			...createNodeInstance(FILTER_NODE_DEFINITIONS['vintage'], { x: 1150, y: 100 }),
		},
		{
			...createNodeInstance(FILTER_NODE_DEFINITIONS['song-difficulty'], { x: 1500, y: 100 }),
		},
		{
			...createNodeInstance(FILTER_NODE_DEFINITIONS['player-score'], { x: 1850, y: 100 }),
		},
		{
			...createNodeInstance(FILTER_NODE_DEFINITIONS['anime-score'], { x: 2200, y: 100 }),
		},
		{
			...createNodeInstance(FILTER_NODE_DEFINITIONS['song-categories'], { x: 2550, y: 100 }),
		},
		{
			...NUMBER_OF_SONGS_CONFIG,
			position: { x: 2900, y: 100 }
		}
	],
	edges: [
		// Connect all nodes in sequence
		{ id: 'e1', source: 'basic-settings', target: 'songs-and-types-*', type: 'default' },
		// Additional edges will be generated dynamically
	]
};
