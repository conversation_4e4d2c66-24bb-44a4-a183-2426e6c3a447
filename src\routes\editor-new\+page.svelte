<script>
	import { SvelteFlow, Controls, Background, MiniMap } from '@xyflow/svelte';
	import '@xyflow/svelte/dist/style.css';

	// Import new node components
	import BasicSettingsNode from '$lib/components/amqplus/new/BasicSettingsNode.svelte';
	import FilterNode from '$lib/components/amqplus/new/FilterNode.svelte';
	import NumberOfSongsNode from '$lib/components/amqplus/new/NumberOfSongsNode.svelte';
	import AddNodesSidebar from '$lib/components/amqplus/new/AddNodesSidebar.svelte';

	import { But<PERSON> } from '$lib/components/ui/button';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';

	// Import node definitions and utilities
	import {
		BASIC_SETTINGS_CONFIG,
		FILTER_NODE_DEFINITIONS,
		NUMBER_OF_SONGS_CONFIG,
		createNodeInstance,
		NODE_CATEGORIES
	} from '$lib/components/amqplus/new/nodeDefinitions.js';

	// Define node types for the new system
	const nodeTypes = {
		basicSettings: BasicSettingsNode,
		filter: FilterNode,
		numberOfSongs: NumberOfSongsNode
	};

	// Node spacing and layout constants
	const NODE_SPACING_X = 320;
	const NODE_SPACING_Y = 100;
	const START_X = 100;
	const START_Y = 100;

	// Sidebar state
	let sidebarOpen = $state(false);

	// Node and edge state
	let nodes = $state([]);
	let edges = $state([]);
	let nodeCounter = $state(0);

	// Calculate horizontal positions for nodes
	function calculateNodePositions() {
		const sortedNodes = [...nodes].sort((a, b) => {
			// Basic settings always first
			if (a.data.type === NODE_CATEGORIES.BASIC_SETTINGS) return -1;
			if (b.data.type === NODE_CATEGORIES.BASIC_SETTINGS) return 1;

			// Number of songs always last
			if (a.data.type === NODE_CATEGORIES.NUMBER_OF_SONGS) return 1;
			if (b.data.type === NODE_CATEGORIES.NUMBER_OF_SONGS) return -1;

			// Sort filters by creation order (using instanceId timestamp)
			return a.data.instanceId.localeCompare(b.data.instanceId);
		});

		// Update positions
		sortedNodes.forEach((node, index) => {
			node.position = {
				x: START_X + (index * NODE_SPACING_X),
				y: START_Y
			};
		});

		// Update edges to connect nodes in sequence
		const newEdges = [];
		for (let i = 0; i < sortedNodes.length - 1; i++) {
			newEdges.push({
				id: `edge-${i}`,
				source: sortedNodes[i].id,
				target: sortedNodes[i + 1].id,
				type: 'default',
				style: { stroke: '#6366f1', strokeWidth: 2 }
			});
		}

		edges = newEdges;
		nodes = sortedNodes;
	}

	// Initialize with default template
	function initializeDefaultTemplate() {
		const initialNodes = [
			// Basic Settings (always first)
			{
				id: 'basic-settings',
				type: NODE_CATEGORIES.BASIC_SETTINGS,
				position: { x: START_X, y: START_Y },
				data: {
					...BASIC_SETTINGS_CONFIG,
					onValueChange: handleNodeValueChange
				},
				deletable: false
			},

			// All filter nodes in logical order
			createNodeInstance(
				FILTER_NODE_DEFINITIONS['songs-and-types'],
				{ x: 0, y: 0 },
				`songs-and-types-${++nodeCounter}`
			),

			createNodeInstance(
				FILTER_NODE_DEFINITIONS['anime-type'],
				{ x: 0, y: 0 },
				`anime-type-${++nodeCounter}`
			),

			createNodeInstance(
				FILTER_NODE_DEFINITIONS['vintage'],
				{ x: 0, y: 0 },
				`vintage-${++nodeCounter}`
			),

			createNodeInstance(
				FILTER_NODE_DEFINITIONS['song-difficulty'],
				{ x: 0, y: 0 },
				`song-difficulty-${++nodeCounter}`
			),

			createNodeInstance(
				FILTER_NODE_DEFINITIONS['player-score'],
				{ x: 0, y: 0 },
				`player-score-${++nodeCounter}`
			),

			createNodeInstance(
				FILTER_NODE_DEFINITIONS['anime-score'],
				{ x: 0, y: 0 },
				`anime-score-${++nodeCounter}`
			),

			createNodeInstance(
				FILTER_NODE_DEFINITIONS['song-categories'],
				{ x: 0, y: 0 },
				`song-categories-${++nodeCounter}`
			),

			// Number of Songs (always last)
			{
				id: 'number-of-songs',
				type: NODE_CATEGORIES.NUMBER_OF_SONGS,
				position: { x: 0, y: 0 },
				data: {
					...NUMBER_OF_SONGS_CONFIG,
					onValueChange: handleNodeValueChange
				},
				deletable: false
			}
		];

		// Add event handlers to filter nodes
		initialNodes.forEach(node => {
			if (node.data.type === NODE_CATEGORIES.FILTER) {
				node.data.onValueChange = handleNodeValueChange;
				node.data.onDelete = handleNodeDelete;
			}
		});

		nodes = initialNodes;
		calculateNodePositions();
	}

	// Handle adding new nodes from sidebar
	function handleAddNode(nodeDefinition) {
		// Check if it's a unique node that already exists
		if (nodeDefinition.unique && nodes.some(n => n.data.id === nodeDefinition.id)) {
			return; // Don't add duplicate unique nodes
		}

		// Create new node instance
		const newNode = createNodeInstance(
			nodeDefinition,
			{ x: 0, y: 0 }, // Position will be calculated
			`${nodeDefinition.id}-${++nodeCounter}`
		);

		// Add event handlers
		newNode.data.onValueChange = handleNodeValueChange;
		newNode.data.onDelete = handleNodeDelete;

		// Insert before the Number of Songs node
		const numberOfSongsIndex = nodes.findIndex(n => n.data.type === NODE_CATEGORIES.NUMBER_OF_SONGS);
		if (numberOfSongsIndex !== -1) {
			nodes.splice(numberOfSongsIndex, 0, newNode);
		} else {
			nodes.push(newNode);
		}

		// Recalculate positions and edges
		calculateNodePositions();

		// Close sidebar
		sidebarOpen = false;
	}

	// Handle node value changes
	function handleNodeValueChange(changeData) {
		const nodeIndex = nodes.findIndex(n => n.id === changeData.nodeId || n.data.instanceId === changeData.nodeId);
		if (nodeIndex !== -1) {
			nodes[nodeIndex].data.currentValue = changeData.newValue;
		}
	}

	// Handle node deletion
	function handleNodeDelete(nodeId) {
		const nodeIndex = nodes.findIndex(n => n.id === nodeId || n.data.instanceId === nodeId);
		if (nodeIndex !== -1 && nodes[nodeIndex].deletable !== false) {
			nodes.splice(nodeIndex, 1);
			calculateNodePositions();
		}
	}

	// Reset to default template
	function resetTemplate() {
		nodeCounter = 0;
		initializeDefaultTemplate();
	}

	// Export configuration
	function exportConfig() {
		const config = {
			nodes: nodes.map(node => ({
				id: node.data.id,
				type: node.data.type,
				instanceId: node.data.instanceId,
				currentValue: node.data.currentValue
			})),
			timestamp: new Date().toISOString()
		};

		console.log('Exported configuration:', config);
		// Here you could download as JSON or save to localStorage
	}

	// Initialize on mount
	$effect(() => {
		initializeDefaultTemplate();
	});
</script>

<svelte:head>
	<title>AMQ Plus - New Node Editor</title>
</svelte:head>

<!-- Fullscreen Editor -->
<main class="absolute inset-0 w-full h-full">
	<!-- Top toolbar -->
	<div class="absolute top-4 left-4 z-10 flex gap-2">
		<Button
			variant="outline"
			size="sm"
			onclick={() => sidebarOpen = !sidebarOpen}
		>
			{sidebarOpen ? 'Hide' : 'Add'} Nodes
		</Button>
		<Button
			variant="outline"
			size="sm"
			onclick={resetTemplate}
		>
			Reset Template
		</Button>
		<Button
			variant="outline"
			size="sm"
			onclick={exportConfig}
		>
			Export Config
		</Button>
	</div>

	<!-- Add Nodes Sidebar -->
	<AddNodesSidebar
		bind:isOpen={sidebarOpen}
		onAddNode={handleAddNode}
		existingNodes={nodes}
	/>

	<!-- Flow Editor -->
	<div class="w-full h-full">
		<SvelteFlow
			{nodes}
			{edges}
			{nodeTypes}
			proOptions={{ hideAttribution: true }}
			nodesDraggable={true}
			nodesConnectable={true}
			elementsSelectable={true}
			zoomOnDoubleClick={false}
		>
			<Background variant="dots" gap={20} size={1} color="rgba(223, 105, 117, 0.1)" />
			<Controls />
		</SvelteFlow>
	</div>
</main>

<style>
	:global(.svelte-flow) {
		background: #fafafa;
	}
</style>
