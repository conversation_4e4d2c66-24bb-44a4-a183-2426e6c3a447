<script>
	import { <PERSON><PERSON>, Position } from '@xyflow/svelte';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { Badge } from '$lib/components/ui/badge';

	let { data } = $props();

	// Local state for the song count
	let songCount = $state(data.currentValue || data.defaultValue || 20);
	let isEditing = $state(false);

	// Sync with prop changes
	$effect(() => {
		if (data.currentValue !== undefined) {
			songCount = data.currentValue;
		}
	});

	// Handle value changes
	function handleValueChange(newValue) {
		const numValue = parseInt(newValue);
		if (!isNaN(numValue) && numValue > 0 && numValue <= 200) {
			songCount = numValue;
			
			// Notify parent of changes
			if (data.onValueChange) {
				data.onValueChange({
					nodeId: data.instanceId,
					newValue: songCount
				});
			}
		}
	}

	// Handle input events
	function handleInput(event) {
		handleValueChange(event.target.value);
	}

	function handleKeydown(event) {
		if (event.key === 'Enter') {
			isEditing = false;
			event.target.blur();
		} else if (event.key === 'Escape') {
			isEditing = false;
			event.target.blur();
		}
	}

	function startEditing() {
		isEditing = true;
	}

	function stopEditing() {
		isEditing = false;
	}

	// Get background color with opacity
	const backgroundColor = $derived(`rgba(220, 38, 38, 0.1)`);
	const borderColor = $derived(data.color);
</script>

<div 
	class="number-of-songs-node relative transition-all duration-200"
	style="background: {backgroundColor}; border: 2px solid {borderColor}; border-radius: 8px; width: 200px;"
>
	<!-- Input handle -->
	<Handle 
		type="target" 
		position={Position.Left} 
		style="width: 12px; height: 12px; background: {data.color}; border: 2px solid white;" 
	/>

	<!-- Final node indicator -->
	<div class="absolute -top-2 -right-2">
		<Badge variant="destructive" class="text-xs">FINAL</Badge>
	</div>

	<Card class="border-0 shadow-none bg-transparent">
		<CardHeader class="pb-3">
			<CardTitle class="flex items-center gap-2 text-lg font-semibold text-gray-800">
				<span class="text-xl">{data.icon}</span>
				{data.title}
			</CardTitle>
			<p class="text-xs text-gray-600">{data.description}</p>
		</CardHeader>

		<CardContent class="pt-0">
			<div class="space-y-3">
				<!-- Song count input -->
				<div class="space-y-2">
					<Label class="text-sm font-medium">Final Song Count</Label>
					
					{#if isEditing}
						<Input
							type="number"
							min="1"
							max="200"
							value={songCount}
							oninput={handleInput}
							onkeydown={handleKeydown}
							onblur={stopEditing}
							class="text-center font-bold text-lg"
							autofocus
						/>
					{:else}
						<div 
							class="bg-white/70 border border-gray-300 rounded-md p-3 text-center cursor-pointer hover:bg-white/90 transition-colors"
							onclick={startEditing}
						>
							<div class="text-2xl font-bold text-gray-800">{songCount}</div>
							<div class="text-xs text-gray-600">songs</div>
						</div>
					{/if}
				</div>

				<!-- Info section -->
				<div class="bg-red-50 border border-red-200 rounded-md p-2">
					<div class="text-xs text-red-700">
						<div class="font-medium mb-1">⚠️ Final Filter</div>
						<div>This node determines the final number of songs that will be selected for the lobby after all filters are applied.</div>
					</div>
				</div>

				<!-- Quick presets -->
				<div class="space-y-2">
					<Label class="text-xs font-medium">Quick Presets</Label>
					<div class="flex flex-wrap gap-1">
						{#each [10, 20, 30, 50, 100] as preset}
							<button
								class="px-2 py-1 text-xs rounded border transition-colors"
								class:bg-red-100={songCount === preset}
								class:border-red-300={songCount === preset}
								class:bg-gray-50={songCount !== preset}
								class:border-gray-300={songCount !== preset}
								onclick={() => handleValueChange(preset)}
							>
								{preset}
							</button>
						{/each}
					</div>
				</div>
			</div>
		</CardContent>
	</Card>
</div>

<style>
	.number-of-songs-node {
		box-shadow: 0 4px 12px rgba(220, 38, 38, 0.2);
	}
</style>
