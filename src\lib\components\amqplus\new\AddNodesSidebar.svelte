<script>
	import { <PERSON><PERSON> } from '$lib/components/ui/button';
	import { <PERSON>, CardContent, CardHeader, CardTit<PERSON> } from '$lib/components/ui/card';
	import { Badge } from '$lib/components/ui/badge';
	import { FILTER_NODE_DEFINITIONS } from './nodeDefinitions.js';

	let { isOpen = $bindable(), onAddNode, existingNodes = [] } = $props();

	// Check if a node type already exists (for unique nodes)
	function nodeExists(nodeId) {
		return existingNodes.some(node => 
			node.data.id === nodeId || 
			(node.data.instanceId && node.data.instanceId.startsWith(nodeId))
		);
	}

	// Handle adding a node
	function handleAddNode(nodeDefinition) {
		if (onAddNode) {
			onAddNode(nodeDefinition);
		}
	}

	// Get node categories for organization
	const nodeCategories = {
		'Content Filters': ['songs-and-types', 'anime-type', 'song-categories'],
		'Quality Filters': ['vintage', 'song-difficulty', 'player-score', 'anime-score']
	};
</script>

<!-- Sidebar overlay -->
{#if isOpen}
	<div 
		class="fixed inset-0 bg-black/20 z-40 transition-opacity"
		onclick={() => isOpen = false}
	></div>
{/if}

<!-- Sidebar panel -->
<div 
	class="fixed top-0 right-0 h-full w-80 bg-white shadow-2xl transform transition-transform duration-300 z-50 border-l border-gray-200"
	class:translate-x-0={isOpen}
	class:translate-x-full={!isOpen}
>
	<div class="flex flex-col h-full">
		<!-- Header -->
		<div class="p-4 border-b border-gray-200">
			<div class="flex items-center justify-between">
				<h2 class="text-lg font-semibold text-gray-800">Add Nodes</h2>
				<Button 
					variant="ghost" 
					size="sm"
					onclick={() => isOpen = false}
				>
					×
				</Button>
			</div>
			<p class="text-sm text-gray-600 mt-1">
				Add filter nodes to customize your quiz configuration
			</p>
		</div>

		<!-- Content -->
		<div class="flex-1 p-4 overflow-y-auto">
			<div class="space-y-6">
				<!-- Info section -->
				<Card class="bg-blue-50 border-blue-200">
					<CardContent class="p-3">
						<div class="text-sm text-blue-800">
							<div class="font-medium mb-1">💡 How it works</div>
							<div class="text-xs space-y-1">
								<div>• Nodes process songs from left to right</div>
								<div>• Each filter reduces the song pool</div>
								<div>• Number of Songs must be the final node</div>
								<div>• Multiple instances of most filters allowed</div>
							</div>
						</div>
					</CardContent>
				</Card>

				<!-- Node categories -->
				{#each Object.entries(nodeCategories) as [categoryName, nodeIds]}
					<div class="space-y-3">
						<h3 class="text-sm font-medium text-gray-700 border-b pb-1">
							{categoryName}
						</h3>
						
						<div class="space-y-2">
							{#each nodeIds as nodeId}
								{@const nodeDefinition = FILTER_NODE_DEFINITIONS[nodeId]}
								{@const alreadyExists = nodeExists(nodeId)}
								
								<Card
									class={`cursor-pointer transition-all duration-200 hover:shadow-md ${
										alreadyExists && nodeDefinition.unique
											? 'opacity-50 cursor-not-allowed'
											: ''
									}`}
									onclick={() => !alreadyExists && handleAddNode(nodeDefinition)}
								>
									<CardContent class="p-3">
										<div class="flex items-start gap-3">
											<!-- Icon -->
											<div 
												class="w-8 h-8 rounded-lg flex items-center justify-center text-sm"
												style="background: rgba({nodeDefinition.color.slice(1).match(/.{2}/g).map(hex => parseInt(hex, 16)).join(', ')}, 0.2); color: {nodeDefinition.color};"
											>
												{nodeDefinition.icon}
											</div>
											
											<!-- Content -->
											<div class="flex-1 min-w-0">
												<div class="flex items-center gap-2 mb-1">
													<h4 class="text-sm font-medium text-gray-800 truncate">
														{nodeDefinition.title}
													</h4>
													{#if alreadyExists}
														<Badge variant="secondary" class="text-xs">Added</Badge>
													{/if}
												</div>
												<p class="text-xs text-gray-600 leading-relaxed">
													{nodeDefinition.description}
												</p>
											</div>
										</div>
									</CardContent>
								</Card>
							{/each}
						</div>
					</div>
				{/each}

				<!-- Special nodes section -->
				<div class="space-y-3">
					<h3 class="text-sm font-medium text-gray-700 border-b pb-1">
						Special Nodes
					</h3>
					
					<!-- Basic Settings info -->
					<Card class="bg-gray-50 border-gray-200">
						<CardContent class="p-3">
							<div class="flex items-start gap-3">
								<div class="w-8 h-8 rounded-lg bg-indigo-100 flex items-center justify-center text-sm text-indigo-600">
									⚙️
								</div>
								<div class="flex-1">
									<div class="flex items-center gap-2 mb-1">
										<h4 class="text-sm font-medium text-gray-800">Basic Settings</h4>
										<Badge variant="outline" class="text-xs">Required</Badge>
									</div>
									<p class="text-xs text-gray-600">
										Core lobby settings (always present, cannot be deleted)
									</p>
								</div>
							</div>
						</CardContent>
					</Card>

					<!-- Number of Songs info -->
					<Card class="bg-red-50 border-red-200">
						<CardContent class="p-3">
							<div class="flex items-start gap-3">
								<div class="w-8 h-8 rounded-lg bg-red-100 flex items-center justify-center text-sm text-red-600">
									🔢
								</div>
								<div class="flex-1">
									<div class="flex items-center gap-2 mb-1">
										<h4 class="text-sm font-medium text-gray-800">Number of Songs</h4>
										<Badge variant="destructive" class="text-xs">Final</Badge>
									</div>
									<p class="text-xs text-gray-600">
										Determines final song count (always last, cannot be deleted)
									</p>
								</div>
							</div>
						</CardContent>
					</Card>
				</div>

				<!-- Tips section -->
				<Card class="bg-yellow-50 border-yellow-200">
					<CardContent class="p-3">
						<div class="text-sm text-yellow-800">
							<div class="font-medium mb-1">💡 Pro Tips</div>
							<div class="text-xs space-y-1">
								<div>• Use multiple filters for complex setups</div>
								<div>• Order matters - filters apply left to right</div>
								<div>• Start broad, then narrow down</div>
								<div>• Test with different song counts</div>
							</div>
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	</div>
</div>
