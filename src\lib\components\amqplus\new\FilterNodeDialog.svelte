<script>
	import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from '$lib/components/ui/dialog';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import { Select, SelectContent, SelectItem, SelectTrigger } from '$lib/components/ui/select';

	let { open = $bindable(), nodeData, value = $bindable(), onSave } = $props();

	// Local working copy of the value
	let workingValue = $state({});

	// Sync working value when dialog opens or value changes
	$effect(() => {
		if (value) {
			workingValue = JSON.parse(JSON.stringify(value));
		}
	});

	// Handle save
	function handleSave() {
		if (onSave) {
			onSave(workingValue);
		}
		open = false;
	}

	// Handle cancel
	function handleCancel() {
		// Reset working value
		workingValue = JSON.parse(JSON.stringify(value));
		open = false;
	}
</script>

<Dialog bind:open>
	<DialogContent class="max-w-md">
		<DialogHeader>
			<DialogTitle class="flex items-center gap-2">
				<span class="text-lg">{nodeData?.icon}</span>
				Configure {nodeData?.title}
			</DialogTitle>
		</DialogHeader>

		<div class="space-y-4">
			{#if nodeData?.id === 'songs-and-types'}
				<!-- Songs & Types Configuration -->
				<div class="space-y-3">
					<Label class="text-sm font-medium">Song Types</Label>
					<div class="space-y-2">
						<div class="flex items-center space-x-2">
							<Checkbox 
								id="openings" 
								bind:checked={workingValue.openings}
							/>
							<Label for="openings" class="text-sm">Openings</Label>
						</div>
						<div class="flex items-center space-x-2">
							<Checkbox 
								id="endings" 
								bind:checked={workingValue.endings}
							/>
							<Label for="endings" class="text-sm">Endings</Label>
						</div>
						<div class="flex items-center space-x-2">
							<Checkbox 
								id="inserts" 
								bind:checked={workingValue.inserts}
							/>
							<Label for="inserts" class="text-sm">Insert Songs</Label>
						</div>
					</div>
				</div>

			{:else if nodeData?.id === 'anime-type'}
				<!-- Anime Type Configuration -->
				<div class="space-y-3">
					<Label class="text-sm font-medium">Anime Types</Label>
					<div class="space-y-2">
						{#each ['tv', 'movie', 'ova', 'ona', 'special'] as type}
							<div class="flex items-center space-x-2">
								<Checkbox 
									id={type} 
									bind:checked={workingValue[type]}
								/>
								<Label for={type} class="text-sm capitalize">{type}</Label>
							</div>
						{/each}
					</div>
				</div>

			{:else if ['song-difficulty', 'player-score', 'anime-score'].includes(nodeData?.id)}
				<!-- Range Configuration -->
				<div class="space-y-3">
					<Label class="text-sm font-medium">Range</Label>
					<div class="grid grid-cols-2 gap-2">
						<div>
							<Label for="min" class="text-xs">Minimum</Label>
							<Input 
								id="min"
								type="number" 
								bind:value={workingValue.min}
								min="0"
								max="100"
							/>
						</div>
						<div>
							<Label for="max" class="text-xs">Maximum</Label>
							<Input 
								id="max"
								type="number" 
								bind:value={workingValue.max}
								min="0"
								max="100"
							/>
						</div>
					</div>
				</div>

			{:else if nodeData?.id === 'vintage'}
				<!-- Vintage Configuration -->
				<div class="space-y-3">
					<Label class="text-sm font-medium">Year Range</Label>
					<div class="text-sm text-gray-600">
						Current: {workingValue.ranges?.[0]?.from?.year || 1944} - {workingValue.ranges?.[0]?.to?.year || 2025}
					</div>
					<Button variant="outline" size="sm">
						Configure Ranges
					</Button>
				</div>

			{:else if nodeData?.id === 'song-categories'}
				<!-- Song Categories Configuration -->
				<div class="space-y-3">
					<Label class="text-sm font-medium">Category Mode</Label>
					<Select bind:value={workingValue.mode}>
						<SelectTrigger>
							{workingValue.mode === 'basic' ? 'Basic' :
							 workingValue.mode === 'advanced' ? 'Advanced' : 'Basic'}
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="basic">Basic</SelectItem>
							<SelectItem value="advanced">Advanced</SelectItem>
						</SelectContent>
					</Select>
					
					<div class="text-sm text-gray-600">
						Included: {workingValue.included?.length || 0} categories<br>
						Excluded: {workingValue.excluded?.length || 0} categories
					</div>
					<Button variant="outline" size="sm">
						Manage Categories
					</Button>
				</div>

			{:else}
				<!-- Generic configuration -->
				<div class="text-sm text-gray-600">
					Configuration options for {nodeData?.title} will be available soon.
				</div>
			{/if}
		</div>

		<!-- Actions -->
		<div class="flex justify-end gap-2 pt-4">
			<Button variant="outline" onclick={handleCancel}>
				Cancel
			</Button>
			<Button onclick={handleSave}>
				Save Changes
			</Button>
		</div>
	</DialogContent>
</Dialog>
