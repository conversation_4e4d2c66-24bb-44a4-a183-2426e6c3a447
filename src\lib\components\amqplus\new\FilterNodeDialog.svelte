<script>
	// Import the mature dialog system from the old editor
	import NodeEditDialog from '$lib/components/amqplus/NodeEditDialog.svelte';
	import { settingConfigs } from '$lib/components/amqplus/dialog/settingsConfig.js';

	let { open = $bindable(), nodeData, value = $bindable(), onSave } = $props();

	// Create a temporary config entry for this node if it doesn't exist
	$effect(() => {
		if (nodeData && nodeData.formType && !settingConfigs[nodeData.id]) {
			settingConfigs[nodeData.id] = {
				type: nodeData.formType,
				label: nodeData.title,
				size: 'fullscreen',
				default: nodeData.defaultValue
			};
		}
	});

	// Transform nodeData to match the expected format for NodeEditDialog
	let transformedNodeData = $state(null);

	// Update transformedNodeData when nodeData or value changes
	$effect(() => {
		transformedNodeData = nodeData ? {
			id: nodeData.id,
			title: nodeData.title,
			icon: nodeData.icon,
			color: nodeData.color,
			currentValue: value
		} : null;
	});

	// Handle save from the mature dialog
	function handleSave(saveData) {
		if (onSave && saveData) {
			onSave(saveData.newValue);
		}
	}
</script>

<!-- Use the mature NodeEditDialog from the old editor -->
<NodeEditDialog
	bind:open
	bind:nodeData={transformedNodeData}
	onSave={handleSave}
/>
